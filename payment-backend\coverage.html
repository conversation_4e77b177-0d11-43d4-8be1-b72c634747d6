
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>handler: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">payment-backend/internal/handler/payment_handler.go (94.8%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package handler

import (
        "net/http"

        "github.com/gin-gonic/gin"
        "github.com/google/uuid"

        "payment-backend/internal/domain"
        "payment-backend/internal/logger"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
        paymentService domain.PaymentService
        logger         logger.Logger
}

// NewPaymentHandler 创建支付处理器
func NewPaymentHandler(paymentService domain.PaymentService, logger logger.Logger) *PaymentHandler <span class="cov8" title="1">{
        return &amp;PaymentHandler{
                paymentService: paymentService,
                logger:         logger,
        }
}</span>

// CreateCheckoutSession 创建结账会话
// @Summary 创建结账会话
// @Description 创建一个新的支付结账会话
// @Tags payment
// @Accept json
// @Produce json
// @Param request body domain.CreateCheckoutSessionRequest true "创建结账会话请求"
// @Success 200 {object} domain.CreateCheckoutSessionResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/pay-service/checkout/session [post]
func (h *PaymentHandler) CreateCheckoutSession(c *gin.Context) <span class="cov8" title="1">{
        var req domain.CreateCheckoutSessionRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov8" title="1">{
                h.logger.Error("Invalid request body", logger.Error(err))
                c.JSON(http.StatusBadRequest, ErrorResponse{
                        Error:   "invalid_request",
                        Message: "Invalid request body: " + err.Error(),
                })
                return
        }</span>

        <span class="cov8" title="1">h.logger.Info("Creating checkout session",
                logger.String("customer_id", req.CustomerID),
                logger.Int64("amount", req.Amount),
                logger.String("currency", string(req.Currency)),
                logger.String("provider", string(req.Provider)),
        )

        response, err := h.paymentService.CreateCheckoutSession(&amp;req)
        if err != nil </span><span class="cov8" title="1">{
                h.logger.Error("Failed to create checkout session", logger.Error(err))
                c.JSON(http.StatusInternalServerError, ErrorResponse{
                        Error:   "internal_error",
                        Message: "Failed to create checkout session",
                })
                return
        }</span>

        <span class="cov8" title="1">h.logger.Info("Checkout session created successfully",
                logger.String("session_id", response.SessionID),
                logger.String("payment_id", response.PaymentID.String()),
        )

        c.JSON(http.StatusOK, response)</span>
}

// GetPayment 获取支付信息
// @Summary 获取支付信息
// @Description 根据支付ID获取支付详情
// @Tags payment
// @Produce json
// @Param id path string true "支付ID"
// @Success 200 {object} domain.Payment
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/pay-service/payments/{id} [get]
func (h *PaymentHandler) GetPayment(c *gin.Context) <span class="cov8" title="1">{
        idStr := c.Param("id")
        paymentID, err := uuid.Parse(idStr)
        if err != nil </span><span class="cov8" title="1">{
                h.logger.Error("Invalid payment ID", logger.Error(err), logger.String("id", idStr))
                c.JSON(http.StatusBadRequest, ErrorResponse{
                        Error:   "invalid_payment_id",
                        Message: "Invalid payment ID format",
                })
                return
        }</span>

        <span class="cov8" title="1">h.logger.Debug("Getting payment", logger.String("payment_id", paymentID.String()))

        payment, err := h.paymentService.GetPayment(paymentID)
        if err != nil </span><span class="cov8" title="1">{
                h.logger.Error("Failed to get payment", logger.Error(err), logger.String("payment_id", paymentID.String()))
                c.JSON(http.StatusNotFound, ErrorResponse{
                        Error:   "payment_not_found",
                        Message: "Payment not found",
                })
                return
        }</span>

        <span class="cov8" title="1">c.JSON(http.StatusOK, payment)</span>
}

// GetPaymentBySessionID 根据会话ID获取支付信息
// @Summary 根据会话ID获取支付信息
// @Description 根据结账会话ID获取支付详情
// @Tags payment
// @Produce json
// @Param session_id path string true "会话ID"
// @Success 200 {object} domain.Payment
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/pay-service/sessions/{session_id}/payment [get]
func (h *PaymentHandler) GetPaymentBySessionID(c *gin.Context) <span class="cov8" title="1">{
        sessionID := c.Param("session_id")
        if sessionID == "" </span><span class="cov8" title="1">{
                h.logger.Error("Missing session ID")
                c.JSON(http.StatusBadRequest, ErrorResponse{
                        Error:   "missing_session_id",
                        Message: "Session ID is required",
                })
                return
        }</span>

        <span class="cov8" title="1">h.logger.Debug("Getting payment by session ID", logger.String("session_id", sessionID))

        payment, err := h.paymentService.GetPaymentBySessionID(sessionID)
        if err != nil </span><span class="cov8" title="1">{
                h.logger.Error("Failed to get payment by session ID", logger.Error(err), logger.String("session_id", sessionID))
                c.JSON(http.StatusNotFound, ErrorResponse{
                        Error:   "payment_not_found",
                        Message: "Payment not found for session",
                })
                return
        }</span>

        <span class="cov8" title="1">c.JSON(http.StatusOK, payment)</span>
}

// ProcessWebhook 处理Webhook
// @Summary 处理支付Webhook
// @Description 处理来自支付提供商的Webhook通知
// @Tags webhook
// @Accept json
// @Param provider path string true "支付提供商" Enums(paypal, stripe)
// @Param X-Signature header string true "Webhook签名"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/pay-service/webhooks/{provider} [post]
func (h *PaymentHandler) ProcessWebhook(c *gin.Context) <span class="cov8" title="1">{
        providerStr := c.Param("provider")
        provider := domain.PaymentProvider(providerStr)

        signature := c.GetHeader("X-Signature")
        if signature == "" </span><span class="cov8" title="1">{
                h.logger.Error("Missing webhook signature")
                c.JSON(http.StatusBadRequest, ErrorResponse{
                        Error:   "missing_signature",
                        Message: "Webhook signature is required",
                })
                return
        }</span>

        <span class="cov8" title="1">payload, err := c.GetRawData()
        if err != nil </span><span class="cov0" title="0">{
                h.logger.Error("Failed to read webhook payload", logger.Error(err))
                c.JSON(http.StatusBadRequest, ErrorResponse{
                        Error:   "invalid_payload",
                        Message: "Failed to read webhook payload",
                })
                return
        }</span>

        <span class="cov8" title="1">h.logger.Info("Processing webhook",
                logger.String("provider", string(provider)),
                logger.Int("payload_size", len(payload)),
        )

        if err := h.paymentService.ProcessWebhook(provider, payload, signature); err != nil </span><span class="cov8" title="1">{
                h.logger.Error("Failed to process webhook", logger.Error(err))
                c.JSON(http.StatusInternalServerError, ErrorResponse{
                        Error:   "webhook_processing_failed",
                        Message: "Failed to process webhook",
                })
                return
        }</span>

        <span class="cov8" title="1">c.JSON(http.StatusOK, gin.H{"status": "success"})</span>
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查服务健康状态
// @Tags health
// @Produce json
// @Success 200 {object} map[string]string
// @Router /health [get]
func (h *PaymentHandler) HealthCheck(c *gin.Context) <span class="cov8" title="1">{
        c.JSON(http.StatusOK, gin.H{
                "status":  "healthy",
                "service": "payment-backend",
        })
}</span>

// ErrorResponse 错误响应
type ErrorResponse struct {
        Error   string `json:"error"`
        Message string `json:"message"`
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
