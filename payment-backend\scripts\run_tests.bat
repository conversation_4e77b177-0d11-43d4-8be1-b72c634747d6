@echo off
setlocal enabledelayedexpansion

REM Payment Backend Test Runner Script for Windows
REM 用于运行各种测试和生成报告的Windows批处理脚本

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 打印带颜色的消息
:print_message
echo %~2
goto :eof

REM 打印标题
:print_title
echo.
echo ==================================================
echo %~1
echo ==================================================
echo.
goto :eof

REM 检查Go环境
:check_go
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: Go 未安装或不在 PATH 中
    exit /b 1
)
for /f "tokens=*" %%i in ('go version') do set "go_version=%%i"
echo Go 版本: !go_version!
goto :eof

REM 运行单元测试
:run_unit_tests
call :print_title "运行单元测试"

echo 运行 Handler 单元测试...
go test ./internal/handler -v -run "^TestPaymentHandler_"

if %errorlevel% equ 0 (
    echo ✅ 单元测试通过
) else (
    echo ❌ 单元测试失败
    exit /b 1
)
goto :eof

REM 运行集成测试
:run_integration_tests
call :print_title "运行集成测试"

echo 运行集成测试套件...
go test ./internal/handler -v -run "TestIntegrationSuite"

if %errorlevel% equ 0 (
    echo ✅ 集成测试通过
) else (
    echo ❌ 集成测试失败
    exit /b 1
)
goto :eof

REM 运行所有测试
:run_all_tests
call :print_title "运行所有测试"

echo 运行所有 Handler 测试...
go test ./internal/handler/... -v

if %errorlevel% equ 0 (
    echo ✅ 所有测试通过
) else (
    echo ❌ 测试失败
    exit /b 1
)
goto :eof

REM 生成覆盖率报告
:generate_coverage
call :print_title "生成测试覆盖率报告"

echo 生成覆盖率数据...
go test ./internal/handler -coverprofile=coverage.out

if exist coverage.out (
    echo 生成覆盖率报告...
    go tool cover -func coverage.out
    
    echo 生成 HTML 覆盖率报告...
    go tool cover -html coverage.out -o coverage.html
    
    echo ✅ 覆盖率报告已生成:
    echo   - 文本报告: 已显示在上方
    echo   - HTML报告: coverage.html
) else (
    echo ❌ 覆盖率文件生成失败
    exit /b 1
)
goto :eof

REM 运行性能测试
:run_benchmark
call :print_title "运行性能测试"

echo 运行基准测试...
go test ./internal/handler -bench=. -benchmem

echo ✅ 性能测试完成
goto :eof

REM 清理测试文件
:cleanup
call :print_title "清理测试文件"

echo 清理临时文件...
if exist coverage.out del coverage.out
if exist coverage.html del coverage.html

echo ✅ 清理完成
goto :eof

REM 显示帮助信息
:show_help
echo Payment Backend 测试运行脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   unit        运行单元测试
echo   integration 运行集成测试
echo   all         运行所有测试 (默认)
echo   coverage    生成测试覆盖率报告
echo   benchmark   运行性能测试
echo   cleanup     清理测试文件
echo   help        显示此帮助信息
echo.
echo 示例:
echo   %~nx0              # 运行所有测试
echo   %~nx0 unit         # 只运行单元测试
echo   %~nx0 coverage     # 生成覆盖率报告
echo   %~nx0 cleanup      # 清理测试文件
goto :eof

REM 主函数
:main
call :check_go

set "action=%~1"
if "%action%"=="" set "action=all"

if "%action%"=="unit" (
    call :run_unit_tests
) else if "%action%"=="integration" (
    call :run_integration_tests
) else if "%action%"=="all" (
    call :run_all_tests
) else if "%action%"=="coverage" (
    call :run_all_tests
    call :generate_coverage
) else if "%action%"=="benchmark" (
    call :run_benchmark
) else if "%action%"=="cleanup" (
    call :cleanup
) else if "%action%"=="help" (
    call :show_help
) else if "%action%"=="-h" (
    call :show_help
) else if "%action%"=="--help" (
    call :show_help
) else (
    echo 错误: 未知选项 '%action%'
    echo.
    call :show_help
    exit /b 1
)

call :print_title "测试完成"
echo 🎉 所有操作已成功完成!
goto :eof

REM 调用主函数
call :main %*
