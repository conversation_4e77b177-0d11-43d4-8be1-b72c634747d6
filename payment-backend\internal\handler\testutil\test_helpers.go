package testutil

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"payment-backend/internal/domain"
	"payment-backend/internal/logger"
)

// MockPaymentService 模拟支付服务
type MockPaymentService struct {
	mock.Mock
}

func (m *MockPaymentService) CreateCheckoutSession(req *domain.CreateCheckoutSessionRequest) (*domain.CreateCheckoutSessionResponse, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.CreateCheckoutSessionResponse), args.Error(1)
}

func (m *MockPaymentService) GetPayment(id uuid.UUID) (*domain.Payment, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Payment), args.Error(1)
}

func (m *MockPaymentService) GetPaymentBySessionID(sessionID string) (*domain.Payment, error) {
	args := m.Called(sessionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Payment), args.Error(1)
}

func (m *MockPaymentService) ProcessWebhook(provider domain.PaymentProvider, payload []byte, signature string) error {
	args := m.Called(provider, payload, signature)
	return args.Error(0)
}

// MockLogger 模拟日志记录器
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Debug(msg string, fields ...logger.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) Info(msg string, fields ...logger.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) Warn(msg string, fields ...logger.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) Error(msg string, fields ...logger.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) Fatal(msg string, fields ...logger.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) With(fields ...logger.Field) logger.Logger {
	args := m.Called(fields)
	return args.Get(0).(logger.Logger)
}

func (m *MockLogger) Sync() error {
	args := m.Called()
	return args.Error(0)
}

// TestData 测试数据构造器
type TestData struct{}

// CreateValidCheckoutSessionRequest 创建有效的结账会话请求
func (td *TestData) CreateValidCheckoutSessionRequest() *domain.CreateCheckoutSessionRequest {
	return &domain.CreateCheckoutSessionRequest{
		CustomerID:  "customer_123",
		Amount:      10000, // $100.00
		Currency:    domain.CurrencyUSD,
		Provider:    domain.PaymentProviderStripe,
		SuccessURL:  "https://example.com/success",
		CancelURL:   "https://example.com/cancel",
		Description: "Test payment",
		Metadata:    map[string]any{"order_id": "order_123"},
		Items: []domain.CheckoutSessionItem{
			{
				Name:        "Test Product",
				Description: "A test product",
				Quantity:    1,
				UnitAmount:  10000,
			},
		},
	}
}

// CreateValidCheckoutSessionResponse 创建有效的结账会话响应
func (td *TestData) CreateValidCheckoutSessionResponse() *domain.CreateCheckoutSessionResponse {
	return &domain.CreateCheckoutSessionResponse{
		SessionID:   "session_123",
		PaymentID:   uuid.New(),
		CheckoutURL: "https://checkout.stripe.com/session_123",
		ExpiresAt:   time.Now().Add(24 * time.Hour),
	}
}

// CreateValidPayment 创建有效的支付对象
func (td *TestData) CreateValidPayment() *domain.Payment {
	now := time.Now()
	return &domain.Payment{
		ID:                uuid.New(),
		SessionID:         "session_123",
		CustomerID:        "customer_123",
		Amount:            10000,
		Currency:          domain.CurrencyUSD,
		Status:            domain.PaymentStatusPending,
		Provider:          domain.PaymentProviderStripe,
		ProviderPaymentID: "pi_123",
		Description:       "Test payment",
		Metadata:          map[string]any{"order_id": "order_123"},
		CreatedAt:         now,
		UpdatedAt:         now,
	}
}

// HTTPTestHelper HTTP测试辅助工具
type HTTPTestHelper struct {
	t *testing.T
}

func NewHTTPTestHelper(t *testing.T) *HTTPTestHelper {
	return &HTTPTestHelper{t: t}
}

// SetupGinTestMode 设置Gin测试模式
func (h *HTTPTestHelper) SetupGinTestMode() {
	gin.SetMode(gin.TestMode)
}

// CreateJSONRequest 创建JSON请求
func (h *HTTPTestHelper) CreateJSONRequest(method, url string, body interface{}) *http.Request {
	var buf bytes.Buffer
	if body != nil {
		err := json.NewEncoder(&buf).Encode(body)
		assert.NoError(h.t, err)
	}

	req, err := http.NewRequest(method, url, &buf)
	assert.NoError(h.t, err)

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return req
}

// CreateRequestWithHeaders 创建带头部的请求
func (h *HTTPTestHelper) CreateRequestWithHeaders(method, url string, body interface{}, headers map[string]string) *http.Request {
	var req *http.Request
	var err error

	// 如果body是字符串，直接使用原始数据
	if bodyStr, ok := body.(string); ok {
		req, err = http.NewRequest(method, url, bytes.NewBufferString(bodyStr))
		assert.NoError(h.t, err)
	} else {
		req = h.CreateJSONRequest(method, url, body)
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	return req
}

// PerformRequest 执行请求并返回响应记录器
func (h *HTTPTestHelper) PerformRequest(router *gin.Engine, req *http.Request) *httptest.ResponseRecorder {
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	return w
}

// AssertJSONResponse 断言JSON响应
func (h *HTTPTestHelper) AssertJSONResponse(w *httptest.ResponseRecorder, expectedStatus int, expectedBody interface{}) {
	assert.Equal(h.t, expectedStatus, w.Code)
	assert.Equal(h.t, "application/json; charset=utf-8", w.Header().Get("Content-Type"))

	if expectedBody != nil {
		expectedJSON, err := json.Marshal(expectedBody)
		assert.NoError(h.t, err)

		assert.JSONEq(h.t, string(expectedJSON), w.Body.String())
	}
}

// AssertErrorResponse 断言错误响应
func (h *HTTPTestHelper) AssertErrorResponse(w *httptest.ResponseRecorder, expectedStatus int, expectedError, expectedMessage string) {
	assert.Equal(h.t, expectedStatus, w.Code)
	assert.Equal(h.t, "application/json; charset=utf-8", w.Header().Get("Content-Type"))

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(h.t, err)

	assert.Equal(h.t, expectedError, response["error"])
	assert.Equal(h.t, expectedMessage, response["message"])
}

// GetTestData 获取测试数据构造器
func GetTestData() *TestData {
	return &TestData{}
}
