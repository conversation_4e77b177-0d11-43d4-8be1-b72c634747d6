package domain

import (
	"time"

	"github.com/google/uuid"
)

// PaymentStatus 支付状态
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusCompleted PaymentStatus = "completed"
	PaymentStatusFailed    PaymentStatus = "failed"
	PaymentStatusCancelled PaymentStatus = "cancelled"
	PaymentStatusRefunded  PaymentStatus = "refunded"
)

// PaymentProvider 支付提供商
type PaymentProvider string

const (
	PaymentProviderPayPal PaymentProvider = "paypal"
	PaymentProviderStripe PaymentProvider = "stripe"
)

// Currency 货币类型
type Currency string

const (
	CurrencyUSD Currency = "USD"
	CurrencyEUR Currency = "EUR"
	CurrencyCNY Currency = "CNY"
)

// Payment 支付实体
type Payment struct {
	ID                uuid.UUID       `json:"id"`
	SessionID         string          `json:"session_id"`
	CustomerID        string          `json:"customer_id"`
	Amount            int64           `json:"amount"` // 以分为单位
	Currency          Currency        `json:"currency"`
	Status            PaymentStatus   `json:"status"`
	Provider          PaymentProvider `json:"provider"`
	ProviderPaymentID string          `json:"provider_payment_id"`
	Description       string          `json:"description"`
	Metadata          map[string]any  `json:"metadata"`
	CreatedAt         time.Time       `json:"created_at"`
	UpdatedAt         time.Time       `json:"updated_at"`
	CompletedAt       *time.Time      `json:"completed_at,omitempty"`
}

// CheckoutSession 结账会话
type CheckoutSession struct {
	ID          string                `json:"id"`
	PaymentID   uuid.UUID             `json:"payment_id"`
	CustomerID  string                `json:"customer_id"`
	Amount      int64                 `json:"amount"`
	Currency    Currency              `json:"currency"`
	Provider    PaymentProvider       `json:"provider"`
	SuccessURL  string                `json:"success_url"`
	CancelURL   string                `json:"cancel_url"`
	Description string                `json:"description"`
	Metadata    map[string]any        `json:"metadata"`
	Items       []CheckoutSessionItem `json:"items"`
	ExpiresAt   time.Time             `json:"expires_at"`
	CreatedAt   time.Time             `json:"created_at"`
}

// CheckoutSessionItem 结账会话项目
type CheckoutSessionItem struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Quantity    int    `json:"quantity"`
	UnitAmount  int64  `json:"unit_amount"` // 以分为单位
}

// CreateCheckoutSessionRequest 创建结账会话请求
type CreateCheckoutSessionRequest struct {
	Amount      int64  `json:"amount" binding:"required,min=1"`
	PriceID     string `json:"priceID" binding:"required"`
	Description string `json:"description"`
}

// CreateCheckoutSessionResponse 创建结账会话响应
type CreateCheckoutSessionResponse struct {
	SessionID   string    `json:"session_id"`
	PaymentID   uuid.UUID `json:"payment_id"`
	CheckoutURL string    `json:"checkout_url"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// PaymentRepository 支付仓储接口
type PaymentRepository interface {
	Create(payment *Payment) error
	GetByID(id uuid.UUID) (*Payment, error)
	GetBySessionID(sessionID string) (*Payment, error)
	Update(payment *Payment) error
	List(customerID string, limit, offset int) ([]*Payment, error)
}

// CheckoutSessionRepository 结账会话仓储接口
type CheckoutSessionRepository interface {
	Create(session *CheckoutSession) error
	GetByID(id string) (*CheckoutSession, error)
	Update(session *CheckoutSession) error
	Delete(id string) error
}

// PaymentService 支付服务接口
type PaymentService interface {
	CreateCheckoutSession(req *CreateCheckoutSessionRequest) (*CreateCheckoutSessionResponse, error)
	GetPayment(id uuid.UUID) (*Payment, error)
	GetPaymentBySessionID(sessionID string) (*Payment, error)
	ProcessWebhook(provider PaymentProvider, payload []byte, signature string) error
}

// PaymentGateway 支付网关接口
type PaymentGateway interface {
	CreateCheckoutSession(session *CheckoutSession) (string, error) // 返回checkout URL
	GetPaymentStatus(providerPaymentID string) (PaymentStatus, error)
	RefundPayment(providerPaymentID string, amount int64) error
	VerifyWebhook(payload []byte, signature string) error
}

// NewPayment 创建新的支付实体
func NewPayment(customerID string, amount int64, currency Currency, provider PaymentProvider, description string) *Payment {
	now := time.Now()
	return &Payment{
		ID:          uuid.New(),
		CustomerID:  customerID,
		Amount:      amount,
		Currency:    currency,
		Status:      PaymentStatusPending,
		Provider:    provider,
		Description: description,
		Metadata:    make(map[string]any),
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// NewCheckoutSession 创建新的结账会话
func NewCheckoutSession(paymentID uuid.UUID, req *CreateCheckoutSessionRequest) *CheckoutSession {
	now := time.Now()
	return &CheckoutSession{
		ID:          uuid.New().String(),
		PaymentID:   paymentID,
		CustomerID:  req.CustomerID,
		Amount:      req.Amount,
		Currency:    req.Currency,
		Provider:    req.Provider,
		SuccessURL:  req.SuccessURL,
		CancelURL:   req.CancelURL,
		Description: req.Description,
		Metadata:    req.Metadata,
		Items:       req.Items,
		ExpiresAt:   now.Add(24 * time.Hour), // 24小时后过期
		CreatedAt:   now,
	}
}

// IsExpired 检查会话是否过期
func (cs *CheckoutSession) IsExpired() bool {
	return time.Now().After(cs.ExpiresAt)
}

// UpdateStatus 更新支付状态
func (p *Payment) UpdateStatus(status PaymentStatus) {
	p.Status = status
	p.UpdatedAt = time.Now()
	if status == PaymentStatusCompleted {
		now := time.Now()
		p.CompletedAt = &now
	}
}

// SetProviderPaymentID 设置提供商支付ID
func (p *Payment) SetProviderPaymentID(providerPaymentID string) {
	p.ProviderPaymentID = providerPaymentID
	p.UpdatedAt = time.Now()
}
