package app

import (
	"go.uber.org/fx"

	"payment-backend/internal/config"
	"payment-backend/internal/domain"
	"payment-backend/internal/gateway"
	"payment-backend/internal/handler"
	"payment-backend/internal/logger"
	"payment-backend/internal/repository/memory"
	"payment-backend/internal/server"
	"payment-backend/internal/service"
)

// ConfigModule 配置模块
var ConfigModule = fx.Module("config",
	fx.Provide(func() (*config.Config, error) {
		return config.LoadConfig("")
	}),
)

// LoggerModule 日志模块
var LoggerModule = fx.Module("logger",
	fx.Provide(func(cfg *config.Config) (logger.Logger, error) {
		return logger.NewLogger(&cfg.Log)
	}),
)

// RepositoryModule 仓储模块
var RepositoryModule = fx.Module("repository",
	fx.Provide(
		fx.Annotate(
			memory.NewPaymentRepository,
			fx.As(new(domain.PaymentRepository)),
		),
		fx.Annotate(
			memory.NewCheckoutSessionRepository,
			fx.As(new(domain.CheckoutSessionRepository)),
		),
	),
)

// GatewayModule 网关模块
var GatewayModule = fx.Module("gateway",
	fx.Provide(
		func(logger logger.Logger) map[domain.PaymentProvider]domain.PaymentGateway {
			return map[domain.PaymentProvider]domain.PaymentGateway{
				domain.PaymentProviderPayPal: gateway.NewMockPaymentGateway(domain.PaymentProviderPayPal, logger),
				domain.PaymentProviderStripe: gateway.NewMockPaymentGateway(domain.PaymentProviderStripe, logger),
			}
		},
	),
)

// ServiceModule 服务模块
var ServiceModule = fx.Module("service",
	fx.Provide(
		fx.Annotate(
			service.NewPaymentService,
			fx.As(new(domain.PaymentService)),
		),
	),
)

// HandlerModule 处理器模块
var HandlerModule = fx.Module("handler",
	fx.Provide(handler.NewPaymentHandler),
)

// AppModule 应用模块
var AppModule = fx.Module("app",
	ConfigModule,
	LoggerModule,
	RepositoryModule,
	GatewayModule,
	ServiceModule,
	HandlerModule,
	server.ServerModule,
)

// NewApp 创建应用
func NewApp() *fx.App {
	return fx.New(
		AppModule,
		fx.NopLogger, // 禁用fx的默认日志，使用我们自己的日志
	)
}
