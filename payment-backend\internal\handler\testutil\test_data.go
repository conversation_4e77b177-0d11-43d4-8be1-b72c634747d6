package testutil

import (
	"time"

	"github.com/google/uuid"

	"payment-backend/internal/domain"
)

// TestDataBuilder 测试数据构建器
type TestDataBuilder struct{}

// NewTestDataBuilder 创建测试数据构建器
func NewTestDataBuilder() *TestDataBuilder {
	return &TestDataBuilder{}
}

// CreateCheckoutSessionRequestBuilder 结账会话请求构建器
type CreateCheckoutSessionRequestBuilder struct {
	req *domain.CreateCheckoutSessionRequest
}

// NewCreateCheckoutSessionRequestBuilder 创建结账会话请求构建器
func (tdb *TestDataBuilder) NewCreateCheckoutSessionRequestBuilder() *CreateCheckoutSessionRequestBuilder {
	return &CreateCheckoutSessionRequestBuilder{
		req: &domain.CreateCheckoutSessionRequest{
			CustomerID:  "customer_123",
			Amount:      10000, // $100.00
			Currency:    domain.CurrencyUSD,
			Provider:    domain.PaymentProviderStripe,
			SuccessURL:  "https://example.com/success",
			CancelURL:   "https://example.com/cancel",
			Description: "Test payment",
			Metadata:    map[string]any{"order_id": "order_123"},
			Items: []domain.CheckoutSessionItem{
				{
					Name:        "Test Product",
					Description: "A test product",
					Quantity:    1,
					UnitAmount:  10000,
				},
			},
		},
	}
}

func (b *CreateCheckoutSessionRequestBuilder) WithCustomerID(customerID string) *CreateCheckoutSessionRequestBuilder {
	b.req.CustomerID = customerID
	return b
}

func (b *CreateCheckoutSessionRequestBuilder) WithAmount(amount int64) *CreateCheckoutSessionRequestBuilder {
	b.req.Amount = amount
	return b
}

func (b *CreateCheckoutSessionRequestBuilder) WithCurrency(currency domain.Currency) *CreateCheckoutSessionRequestBuilder {
	b.req.Currency = currency
	return b
}

func (b *CreateCheckoutSessionRequestBuilder) WithProvider(provider domain.PaymentProvider) *CreateCheckoutSessionRequestBuilder {
	b.req.Provider = provider
	return b
}

func (b *CreateCheckoutSessionRequestBuilder) WithSuccessURL(url string) *CreateCheckoutSessionRequestBuilder {
	b.req.SuccessURL = url
	return b
}

func (b *CreateCheckoutSessionRequestBuilder) WithCancelURL(url string) *CreateCheckoutSessionRequestBuilder {
	b.req.CancelURL = url
	return b
}

func (b *CreateCheckoutSessionRequestBuilder) WithDescription(description string) *CreateCheckoutSessionRequestBuilder {
	b.req.Description = description
	return b
}

func (b *CreateCheckoutSessionRequestBuilder) WithMetadata(metadata map[string]any) *CreateCheckoutSessionRequestBuilder {
	b.req.Metadata = metadata
	return b
}

func (b *CreateCheckoutSessionRequestBuilder) WithItems(items []domain.CheckoutSessionItem) *CreateCheckoutSessionRequestBuilder {
	b.req.Items = items
	return b
}

func (b *CreateCheckoutSessionRequestBuilder) Build() *domain.CreateCheckoutSessionRequest {
	return b.req
}

// PaymentBuilder 支付对象构建器
type PaymentBuilder struct {
	payment *domain.Payment
}

// NewPaymentBuilder 创建支付对象构建器
func (tdb *TestDataBuilder) NewPaymentBuilder() *PaymentBuilder {
	now := time.Now()
	return &PaymentBuilder{
		payment: &domain.Payment{
			ID:                uuid.New(),
			SessionID:         "session_123",
			CustomerID:        "customer_123",
			Amount:            10000,
			Currency:          domain.CurrencyUSD,
			Status:            domain.PaymentStatusPending,
			Provider:          domain.PaymentProviderStripe,
			ProviderPaymentID: "pi_123",
			Description:       "Test payment",
			Metadata:          map[string]any{"order_id": "order_123"},
			CreatedAt:         now,
			UpdatedAt:         now,
		},
	}
}

func (b *PaymentBuilder) WithID(id uuid.UUID) *PaymentBuilder {
	b.payment.ID = id
	return b
}

func (b *PaymentBuilder) WithSessionID(sessionID string) *PaymentBuilder {
	b.payment.SessionID = sessionID
	return b
}

func (b *PaymentBuilder) WithCustomerID(customerID string) *PaymentBuilder {
	b.payment.CustomerID = customerID
	return b
}

func (b *PaymentBuilder) WithAmount(amount int64) *PaymentBuilder {
	b.payment.Amount = amount
	return b
}

func (b *PaymentBuilder) WithCurrency(currency domain.Currency) *PaymentBuilder {
	b.payment.Currency = currency
	return b
}

func (b *PaymentBuilder) WithStatus(status domain.PaymentStatus) *PaymentBuilder {
	b.payment.Status = status
	return b
}

func (b *PaymentBuilder) WithProvider(provider domain.PaymentProvider) *PaymentBuilder {
	b.payment.Provider = provider
	return b
}

func (b *PaymentBuilder) WithProviderPaymentID(providerPaymentID string) *PaymentBuilder {
	b.payment.ProviderPaymentID = providerPaymentID
	return b
}

func (b *PaymentBuilder) WithDescription(description string) *PaymentBuilder {
	b.payment.Description = description
	return b
}

func (b *PaymentBuilder) WithMetadata(metadata map[string]any) *PaymentBuilder {
	b.payment.Metadata = metadata
	return b
}

func (b *PaymentBuilder) WithCreatedAt(createdAt time.Time) *PaymentBuilder {
	b.payment.CreatedAt = createdAt
	return b
}

func (b *PaymentBuilder) WithUpdatedAt(updatedAt time.Time) *PaymentBuilder {
	b.payment.UpdatedAt = updatedAt
	return b
}

func (b *PaymentBuilder) WithCompletedAt(completedAt *time.Time) *PaymentBuilder {
	b.payment.CompletedAt = completedAt
	return b
}

func (b *PaymentBuilder) Build() *domain.Payment {
	return b.payment
}

// CheckoutSessionResponseBuilder 结账会话响应构建器
type CheckoutSessionResponseBuilder struct {
	response *domain.CreateCheckoutSessionResponse
}

// NewCheckoutSessionResponseBuilder 创建结账会话响应构建器
func (tdb *TestDataBuilder) NewCheckoutSessionResponseBuilder() *CheckoutSessionResponseBuilder {
	return &CheckoutSessionResponseBuilder{
		response: &domain.CreateCheckoutSessionResponse{
			SessionID:   "session_123",
			PaymentID:   uuid.New(),
			CheckoutURL: "https://checkout.stripe.com/session_123",
			ExpiresAt:   time.Now().Add(24 * time.Hour),
		},
	}
}

func (b *CheckoutSessionResponseBuilder) WithSessionID(sessionID string) *CheckoutSessionResponseBuilder {
	b.response.SessionID = sessionID
	return b
}

func (b *CheckoutSessionResponseBuilder) WithPaymentID(paymentID uuid.UUID) *CheckoutSessionResponseBuilder {
	b.response.PaymentID = paymentID
	return b
}

func (b *CheckoutSessionResponseBuilder) WithCheckoutURL(checkoutURL string) *CheckoutSessionResponseBuilder {
	b.response.CheckoutURL = checkoutURL
	return b
}

func (b *CheckoutSessionResponseBuilder) WithExpiresAt(expiresAt time.Time) *CheckoutSessionResponseBuilder {
	b.response.ExpiresAt = expiresAt
	return b
}

func (b *CheckoutSessionResponseBuilder) Build() *domain.CreateCheckoutSessionResponse {
	return b.response
}

// 常用的测试数据常量
const (
	TestCustomerID        = "customer_test_123"
	TestSessionID         = "session_test_123"
	TestProviderPaymentID = "pi_test_123"
	TestOrderID           = "order_test_123"
	TestAmount            = int64(10000) // $100.00
	TestDescription       = "Test payment description"
	TestSuccessURL        = "https://example.com/success"
	TestCancelURL         = "https://example.com/cancel"
	TestCheckoutURL       = "https://checkout.stripe.com/session_test_123"
	TestWebhookSignature  = "test_webhook_signature"
)

// 常用的测试错误消息
const (
	ErrInvalidRequest       = "invalid_request"
	ErrInvalidPaymentID     = "invalid_payment_id"
	ErrMissingSessionID     = "missing_session_id"
	ErrMissingSignature     = "missing_signature"
	ErrPaymentNotFound      = "payment_not_found"
	ErrInternalError        = "internal_error"
	ErrWebhookProcessFailed = "webhook_processing_failed"
)

// 常用的测试错误消息内容
const (
	MsgInvalidRequestBody            = "Invalid request body"
	MsgInvalidPaymentIDFormat        = "Invalid payment ID format"
	MsgSessionIDRequired             = "Session ID is required"
	MsgWebhookSignatureRequired      = "Webhook signature is required"
	MsgPaymentNotFound               = "Payment not found"
	MsgPaymentNotFoundForSession     = "Payment not found for session"
	MsgFailedToCreateCheckoutSession = "Failed to create checkout session"
	MsgFailedToProcessWebhook        = "Failed to process webhook"
)
