# Payment Backend 配置指南

## 配置概述

Payment Backend 支持多种配置方式，按优先级从高到低排列：

1. **环境变量** (最高优先级)
2. **配置文件** (中等优先级)
3. **默认值** (最低优先级，兜底)

## 配置方式

### 1. 配置文件

#### 主配置文件
- `configs/config.yaml` - 生产环境配置
- `configs/config.dev.yaml` - 开发环境配置

#### 使用方法
```bash
# 使用默认配置文件
./payment-backend serve

# 指定配置文件
./payment-backend serve --config configs/config.dev.yaml
```

### 2. 环境变量

#### 命名规则
- 前缀：`PAYMENT_`
- 层级分隔符：`_`（替换配置文件中的 `.`）
- 大写字母

#### 示例
```bash
# 配置文件中的 server.host 对应环境变量
export PAYMENT_SERVER_HOST=localhost

# 配置文件中的 payment.providers.stripe.api_key 对应环境变量
export PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY=sk_live_xxxxx
```

#### 环境变量配置示例
参考 `configs/.env.example` 文件：

```bash
# 复制示例文件
cp configs/.env.example .env

# 编辑配置
vim .env

# 加载环境变量
source .env

# 启动服务
./payment-backend serve
```

### 3. 默认值

当配置文件和环境变量都未设置时，系统会使用内置的默认值：

```go
// 服务器默认配置
server.host = "0.0.0.0"
server.port = 8080
server.mode = "debug"

// 数据库默认配置
database.driver = "postgres"
database.host = "localhost"
database.port = 5432

// 支付默认配置
payment.providers.stripe.enabled = false
payment.providers.paypal.enabled = false

// 日志默认配置
log.level = "info"
log.format = "json"
log.output = "stdout"
```

## 配置项详解

### 服务器配置 (server)

| 配置项 | 环境变量 | 默认值 | 说明 |
|--------|----------|--------|------|
| host | PAYMENT_SERVER_HOST | 0.0.0.0 | 服务监听地址 |
| port | PAYMENT_SERVER_PORT | 8080 | 服务监听端口 |
| read_timeout | PAYMENT_SERVER_READ_TIMEOUT | 30 | 读取超时时间(秒) |
| write_timeout | PAYMENT_SERVER_WRITE_TIMEOUT | 30 | 写入超时时间(秒) |
| mode | PAYMENT_SERVER_MODE | debug | 运行模式: debug/release/test |

### 数据库配置 (database)

| 配置项 | 环境变量 | 默认值 | 说明 |
|--------|----------|--------|------|
| driver | PAYMENT_DATABASE_DRIVER | postgres | 数据库驱动 |
| host | PAYMENT_DATABASE_HOST | localhost | 数据库主机 |
| port | PAYMENT_DATABASE_PORT | 5432 | 数据库端口 |
| username | PAYMENT_DATABASE_USERNAME | - | 数据库用户名 |
| password | PAYMENT_DATABASE_PASSWORD | - | 数据库密码 |
| database | PAYMENT_DATABASE_DATABASE | - | 数据库名称 |
| ssl_mode | PAYMENT_DATABASE_SSL_MODE | disable | SSL模式 |

### 支付配置 (payment)

#### Stripe 配置

| 配置项 | 环境变量 | 默认值 | 说明 |
|--------|----------|--------|------|
| enabled | PAYMENT_PAYMENT_PROVIDERS_STRIPE_ENABLED | false | 是否启用 |
| api_key | PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY | - | API密钥 |
| secret_key | PAYMENT_PAYMENT_PROVIDERS_STRIPE_SECRET_KEY | - | 密钥 |
| webhook.url | PAYMENT_PAYMENT_PROVIDERS_STRIPE_WEBHOOK_URL | - | Webhook URL |
| webhook.secret | PAYMENT_PAYMENT_PROVIDERS_STRIPE_WEBHOOK_SECRET | - | Webhook密钥 |
| settings.environment | PAYMENT_PAYMENT_PROVIDERS_STRIPE_SETTINGS_ENVIRONMENT | test | 环境: test/live |

#### PayPal 配置

| 配置项 | 环境变量 | 默认值 | 说明 |
|--------|----------|--------|------|
| enabled | PAYMENT_PAYMENT_PROVIDERS_PAYPAL_ENABLED | false | 是否启用 |
| api_key | PAYMENT_PAYMENT_PROVIDERS_PAYPAL_API_KEY | - | API密钥 |
| secret_key | PAYMENT_PAYMENT_PROVIDERS_PAYPAL_SECRET_KEY | - | 密钥 |
| webhook.url | PAYMENT_PAYMENT_PROVIDERS_PAYPAL_WEBHOOK_URL | - | Webhook URL |
| webhook.secret | PAYMENT_PAYMENT_PROVIDERS_PAYPAL_WEBHOOK_SECRET | - | Webhook密钥 |
| settings.environment | PAYMENT_PAYMENT_PROVIDERS_PAYPAL_SETTINGS_ENVIRONMENT | sandbox | 环境: sandbox/live |

### 日志配置 (log)

| 配置项 | 环境变量 | 默认值 | 说明 |
|--------|----------|--------|------|
| level | PAYMENT_LOG_LEVEL | info | 日志级别: debug/info/warn/error |
| format | PAYMENT_LOG_FORMAT | json | 日志格式: json/console |
| output | PAYMENT_LOG_OUTPUT | stdout | 输出方式: stdout/file |
| filename | PAYMENT_LOG_FILENAME | - | 日志文件路径 |
| max_size | PAYMENT_LOG_MAX_SIZE | 100 | 最大文件大小(MB) |
| max_backups | PAYMENT_LOG_MAX_BACKUPS | 3 | 备份文件数量 |
| max_age | PAYMENT_LOG_MAX_AGE | 28 | 保留天数 |

## 部署场景示例

### 开发环境

```bash
# 使用开发配置文件
./payment-backend serve --config configs/config.dev.yaml

# 或使用环境变量覆盖
export PAYMENT_SERVER_MODE=debug
export PAYMENT_LOG_LEVEL=debug
export PAYMENT_LOG_FORMAT=console
./payment-backend serve
```

### 生产环境

```bash
# 使用环境变量配置敏感信息
export PAYMENT_SERVER_MODE=release
export PAYMENT_DATABASE_PASSWORD=secure_password
export PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY=sk_live_xxxxx
export PAYMENT_PAYMENT_PROVIDERS_STRIPE_SECRET_KEY=sk_live_secret_xxxxx
export PAYMENT_LOG_LEVEL=warn

# 启动服务
./payment-backend serve
```

### Docker 部署

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o payment-backend ./cmd

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/payment-backend .
COPY --from=builder /app/configs ./configs
CMD ["./payment-backend", "serve"]
```

```bash
# 使用环境变量启动容器
docker run -d \
  -e PAYMENT_SERVER_HOST=0.0.0.0 \
  -e PAYMENT_SERVER_PORT=8080 \
  -e PAYMENT_DATABASE_HOST=db \
  -e PAYMENT_DATABASE_PASSWORD=secure_password \
  -e PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY=sk_live_xxxxx \
  payment-backend
```

### Kubernetes 部署

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: payment-backend-config
data:
  PAYMENT_SERVER_HOST: "0.0.0.0"
  PAYMENT_SERVER_PORT: "8080"
  PAYMENT_LOG_LEVEL: "info"
  PAYMENT_LOG_FORMAT: "json"

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: payment-backend-secret
type: Opaque
stringData:
  PAYMENT_DATABASE_PASSWORD: "secure_password"
  PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY: "sk_live_xxxxx"
  PAYMENT_PAYMENT_PROVIDERS_STRIPE_SECRET_KEY: "sk_live_secret_xxxxx"

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: payment-backend
  template:
    metadata:
      labels:
        app: payment-backend
    spec:
      containers:
      - name: payment-backend
        image: payment-backend:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: payment-backend-config
        - secretRef:
            name: payment-backend-secret
```

## 配置验证

### 检查当前配置

```bash
# 启动时会显示使用的配置文件
./payment-backend serve
# 输出: Using config file: /path/to/config.yaml
```

### 配置测试

```bash
# 测试配置是否正确
./payment-backend serve --config configs/config.dev.yaml --dry-run
```

## 最佳实践

1. **敏感信息使用环境变量**：API密钥、数据库密码等
2. **基础配置使用配置文件**：服务器端口、日志级别等
3. **提供默认值**：确保服务在最小配置下也能启动
4. **环境隔离**：不同环境使用不同的配置文件
5. **配置验证**：启动时验证必要的配置项

## 故障排除

### 常见问题

1. **配置文件找不到**
   ```
   Error: Config File "config" Not Found
   ```
   解决：检查配置文件路径，或使用 `--config` 参数指定

2. **环境变量格式错误**
   ```
   Error: invalid character 'x' looking for beginning of value
   ```
   解决：检查环境变量名称是否正确，注意大小写和分隔符

3. **配置项类型错误**
   ```
   Error: cannot unmarshal string into Go value of type int
   ```
   解决：检查配置值的类型，如端口号应为数字

### 调试技巧

```bash
# 查看当前环境变量
env | grep PAYMENT_

# 验证配置加载
./payment-backend serve --config configs/config.dev.yaml --verbose
```
