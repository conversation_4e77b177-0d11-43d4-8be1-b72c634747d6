package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"payment-backend/internal/domain"
	"payment-backend/internal/logger"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	paymentService domain.PaymentService
	logger         logger.Logger
}

// NewPaymentHandler 创建支付处理器
func NewPaymentHandler(paymentService domain.PaymentService, logger logger.Logger) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
		logger:         logger,
	}
}

// CreateCheckoutSession 创建结账会话
// @Summary 创建结账会话
// @Description 创建一个新的支付结账会话
// @Tags payment
// @Accept json
// @Produce json
// @Param request body domain.CreateCheckoutSessionRequest true "创建结账会话请求"
// @Success 200 {object} domain.CreateCheckoutSessionResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/pay-service/checkout/session [post]
func (h *PaymentHandler) CreateCheckoutSession(c *gin.Context) {
	var req domain.CreateCheckoutSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logger.Error(err))
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	h.logger.Info("Creating checkout session",
		logger.String("customer_id", req.CustomerID),
		logger.Int64("amount", req.Amount),
		logger.String("currency", string(req.Currency)),
		logger.String("provider", string(req.Provider)),
	)

	response, err := h.paymentService.CreateCheckoutSession(&req)
	if err != nil {
		h.logger.Error("Failed to create checkout session", logger.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "internal_error",
			Message: "Failed to create checkout session",
		})
		return
	}

	h.logger.Info("Checkout session created successfully",
		logger.String("session_id", response.SessionID),
		logger.String("payment_id", response.PaymentID.String()),
	)

	c.JSON(http.StatusOK, response)
}

// GetPayment 获取支付信息
// @Summary 获取支付信息
// @Description 根据支付ID获取支付详情
// @Tags payment
// @Produce json
// @Param id path string true "支付ID"
// @Success 200 {object} domain.Payment
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/pay-service/payments/{id} [get]
func (h *PaymentHandler) GetPayment(c *gin.Context) {
	idStr := c.Param("id")
	paymentID, err := uuid.Parse(idStr)
	if err != nil {
		h.logger.Error("Invalid payment ID", logger.Error(err), logger.String("id", idStr))
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_payment_id",
			Message: "Invalid payment ID format",
		})
		return
	}

	h.logger.Debug("Getting payment", logger.String("payment_id", paymentID.String()))

	payment, err := h.paymentService.GetPayment(paymentID)
	if err != nil {
		h.logger.Error("Failed to get payment", logger.Error(err), logger.String("payment_id", paymentID.String()))
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "payment_not_found",
			Message: "Payment not found",
		})
		return
	}

	c.JSON(http.StatusOK, payment)
}

// GetPaymentBySessionID 根据会话ID获取支付信息
// @Summary 根据会话ID获取支付信息
// @Description 根据结账会话ID获取支付详情
// @Tags payment
// @Produce json
// @Param session_id path string true "会话ID"
// @Success 200 {object} domain.Payment
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/pay-service/sessions/{session_id}/payment [get]
func (h *PaymentHandler) GetPaymentBySessionID(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		h.logger.Error("Missing session ID")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_session_id",
			Message: "Session ID is required",
		})
		return
	}

	h.logger.Debug("Getting payment by session ID", logger.String("session_id", sessionID))

	payment, err := h.paymentService.GetPaymentBySessionID(sessionID)
	if err != nil {
		h.logger.Error("Failed to get payment by session ID", logger.Error(err), logger.String("session_id", sessionID))
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "payment_not_found",
			Message: "Payment not found for session",
		})
		return
	}

	c.JSON(http.StatusOK, payment)
}

// ProcessWebhook 处理Webhook
// @Summary 处理支付Webhook
// @Description 处理来自支付提供商的Webhook通知
// @Tags webhook
// @Accept json
// @Param provider path string true "支付提供商" Enums(paypal, stripe)
// @Param X-Signature header string true "Webhook签名"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/pay-service/webhooks/{provider} [post]
func (h *PaymentHandler) ProcessWebhook(c *gin.Context) {
	providerStr := c.Param("provider")
	provider := domain.PaymentProvider(providerStr)

	signature := c.GetHeader("X-Signature")
	if signature == "" {
		h.logger.Error("Missing webhook signature")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_signature",
			Message: "Webhook signature is required",
		})
		return
	}

	payload, err := c.GetRawData()
	if err != nil {
		h.logger.Error("Failed to read webhook payload", logger.Error(err))
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_payload",
			Message: "Failed to read webhook payload",
		})
		return
	}

	h.logger.Info("Processing webhook",
		logger.String("provider", string(provider)),
		logger.Int("payload_size", len(payload)),
	)

	if err := h.paymentService.ProcessWebhook(provider, payload, signature); err != nil {
		h.logger.Error("Failed to process webhook", logger.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "webhook_processing_failed",
			Message: "Failed to process webhook",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查服务健康状态
// @Tags health
// @Produce json
// @Success 200 {object} map[string]string
// @Router /health [get]
func (h *PaymentHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "healthy",
		"service": "payment-backend",
	})
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
}
