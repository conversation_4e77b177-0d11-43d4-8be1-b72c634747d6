package handler

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"payment-backend/internal/domain"
	"payment-backend/internal/handler/testutil"
)

func TestPaymentHandler_CreateCheckoutSession(t *testing.T) {
	testData := testutil.GetTestData()
	helper := testutil.NewHTTPTestHelper(t)
	helper.SetupGinTestMode()

	tests := []struct {
		name           string
		requestBody    interface{}
		setupMocks     func(*testutil.MockPaymentService, *testutil.MockLogger)
		expectedStatus int
		expectedError  string
		expectedMsg    string
	}{
		{
			name:        "成功创建结账会话",
			requestBody: testData.CreateValidCheckoutSessionRequest(),
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				response := testData.CreateValidCheckoutSessionResponse()
				mockService.On("CreateCheckoutSession", mock.AnythingOfType("*domain.CreateCheckoutSessionRequest")).Return(response, nil)
				mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:        "无效的请求体",
			requestBody: map[string]interface{}{"invalid": "data"},
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "invalid_request",
			expectedMsg:    "Invalid request body",
		},
		{
			name: "缺少必填字段",
			requestBody: map[string]interface{}{
				"amount":   10000,
				"currency": "USD",
			},
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "invalid_request",
			expectedMsg:    "Invalid request body",
		},
		{
			name:        "服务层错误",
			requestBody: testData.CreateValidCheckoutSessionRequest(),
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockService.On("CreateCheckoutSession", mock.AnythingOfType("*domain.CreateCheckoutSessionRequest")).Return(nil, errors.New("service error"))
				mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
				mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusInternalServerError,
			expectedError:  "internal_error",
			expectedMsg:    "Failed to create checkout session",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象
			mockService := &testutil.MockPaymentService{}
			mockLogger := &testutil.MockLogger{}
			tt.setupMocks(mockService, mockLogger)

			// 创建处理器
			handler := NewPaymentHandler(mockService, mockLogger)

			// 设置路由
			router := gin.New()
			router.POST("/checkout/session", handler.CreateCheckoutSession)

			// 创建请求
			req := helper.CreateJSONRequest("POST", "/checkout/session", tt.requestBody)

			// 执行请求
			w := helper.PerformRequest(router, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.expectedError != "" {
				assert.Equal(t, "application/json; charset=utf-8", w.Header().Get("Content-Type"))
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedError, response["error"])
				if tt.expectedMsg != "" {
					assert.Contains(t, response["message"], tt.expectedMsg)
				}
			}

			// 验证模拟对象调用
			mockService.AssertExpectations(t)
			mockLogger.AssertExpectations(t)
		})
	}
}

func TestPaymentHandler_GetPayment(t *testing.T) {
	testData := testutil.GetTestData()
	helper := testutil.NewHTTPTestHelper(t)
	helper.SetupGinTestMode()

	validPaymentID := uuid.New()
	invalidPaymentID := "invalid-uuid"

	tests := []struct {
		name           string
		paymentID      string
		setupMocks     func(*testutil.MockPaymentService, *testutil.MockLogger)
		expectedStatus int
		expectedError  string
		expectedMsg    string
	}{
		{
			name:      "成功获取支付信息",
			paymentID: validPaymentID.String(),
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				payment := testData.CreateValidPayment()
				payment.ID = validPaymentID
				mockService.On("GetPayment", validPaymentID).Return(payment, nil)
				mockLogger.On("Debug", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:      "无效的支付ID格式",
			paymentID: invalidPaymentID,
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "invalid_payment_id",
			expectedMsg:    "Invalid payment ID format",
		},
		{
			name:      "支付不存在",
			paymentID: validPaymentID.String(),
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockService.On("GetPayment", validPaymentID).Return(nil, errors.New("payment not found"))
				mockLogger.On("Debug", mock.AnythingOfType("string"), mock.Anything).Return()
				mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusNotFound,
			expectedError:  "payment_not_found",
			expectedMsg:    "Payment not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象
			mockService := &testutil.MockPaymentService{}
			mockLogger := &testutil.MockLogger{}
			tt.setupMocks(mockService, mockLogger)

			// 创建处理器
			handler := NewPaymentHandler(mockService, mockLogger)

			// 设置路由
			router := gin.New()
			router.GET("/payments/:id", handler.GetPayment)

			// 创建请求
			url := fmt.Sprintf("/payments/%s", tt.paymentID)
			req := helper.CreateJSONRequest("GET", url, nil)

			// 执行请求
			w := helper.PerformRequest(router, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.expectedError != "" {
				helper.AssertErrorResponse(w, tt.expectedStatus, tt.expectedError, tt.expectedMsg)
			}

			// 验证模拟对象调用
			mockService.AssertExpectations(t)
			mockLogger.AssertExpectations(t)
		})
	}
}

func TestPaymentHandler_GetPaymentBySessionID(t *testing.T) {
	testData := testutil.GetTestData()
	helper := testutil.NewHTTPTestHelper(t)
	helper.SetupGinTestMode()

	validSessionID := "session_123"
	emptySessionID := ""

	tests := []struct {
		name           string
		sessionID      string
		setupMocks     func(*testutil.MockPaymentService, *testutil.MockLogger)
		expectedStatus int
		expectedError  string
		expectedMsg    string
	}{
		{
			name:      "成功根据会话ID获取支付信息",
			sessionID: validSessionID,
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				payment := testData.CreateValidPayment()
				payment.SessionID = validSessionID
				mockService.On("GetPaymentBySessionID", validSessionID).Return(payment, nil)
				mockLogger.On("Debug", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:      "空的会话ID",
			sessionID: emptySessionID,
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "missing_session_id",
			expectedMsg:    "Session ID is required",
		},
		{
			name:      "会话对应的支付不存在",
			sessionID: validSessionID,
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockService.On("GetPaymentBySessionID", validSessionID).Return(nil, errors.New("payment not found"))
				mockLogger.On("Debug", mock.AnythingOfType("string"), mock.Anything).Return()
				mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusNotFound,
			expectedError:  "payment_not_found",
			expectedMsg:    "Payment not found for session",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象
			mockService := &testutil.MockPaymentService{}
			mockLogger := &testutil.MockLogger{}
			tt.setupMocks(mockService, mockLogger)

			// 创建处理器
			handler := NewPaymentHandler(mockService, mockLogger)

			// 设置路由
			router := gin.New()
			router.GET("/sessions/:session_id/payment", handler.GetPaymentBySessionID)

			// 创建请求
			url := fmt.Sprintf("/sessions/%s/payment", tt.sessionID)
			req := helper.CreateJSONRequest("GET", url, nil)

			// 执行请求
			w := helper.PerformRequest(router, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.expectedError != "" {
				helper.AssertErrorResponse(w, tt.expectedStatus, tt.expectedError, tt.expectedMsg)
			}

			// 验证模拟对象调用
			mockService.AssertExpectations(t)
			mockLogger.AssertExpectations(t)
		})
	}
}

func TestPaymentHandler_ProcessWebhook(t *testing.T) {
	helper := testutil.NewHTTPTestHelper(t)
	helper.SetupGinTestMode()

	validProvider := "stripe"
	validSignature := "valid_signature"
	emptySignature := ""
	validPayload := []byte(`{"event": "payment.completed"}`)

	tests := []struct {
		name           string
		provider       string
		signature      string
		payload        []byte
		setupMocks     func(*testutil.MockPaymentService, *testutil.MockLogger)
		expectedStatus int
		expectedError  string
		expectedMsg    string
	}{
		{
			name:      "成功处理Webhook",
			provider:  validProvider,
			signature: validSignature,
			payload:   validPayload,
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockService.On("ProcessWebhook", domain.PaymentProvider(validProvider), validPayload, validSignature).Return(nil)
				mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:      "缺少签名",
			provider:  validProvider,
			signature: emptySignature,
			payload:   validPayload,
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "missing_signature",
			expectedMsg:    "Webhook signature is required",
		},
		{
			name:      "处理Webhook失败",
			provider:  validProvider,
			signature: validSignature,
			payload:   validPayload,
			setupMocks: func(mockService *testutil.MockPaymentService, mockLogger *testutil.MockLogger) {
				mockService.On("ProcessWebhook", domain.PaymentProvider(validProvider), validPayload, validSignature).Return(errors.New("webhook processing failed"))
				mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
				mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
			},
			expectedStatus: http.StatusInternalServerError,
			expectedError:  "webhook_processing_failed",
			expectedMsg:    "Failed to process webhook",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象
			mockService := &testutil.MockPaymentService{}
			mockLogger := &testutil.MockLogger{}
			tt.setupMocks(mockService, mockLogger)

			// 创建处理器
			handler := NewPaymentHandler(mockService, mockLogger)

			// 设置路由
			router := gin.New()
			router.POST("/webhooks/:provider", handler.ProcessWebhook)

			// 创建请求
			url := fmt.Sprintf("/webhooks/%s", tt.provider)
			headers := make(map[string]string)
			if tt.signature != "" {
				headers["X-Signature"] = tt.signature
			}
			req := helper.CreateRequestWithHeaders("POST", url, string(tt.payload), headers)

			// 执行请求
			w := helper.PerformRequest(router, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.expectedError != "" {
				helper.AssertErrorResponse(w, tt.expectedStatus, tt.expectedError, tt.expectedMsg)
			} else {
				// 验证成功响应
				assert.Equal(t, "application/json; charset=utf-8", w.Header().Get("Content-Type"))
				assert.Contains(t, w.Body.String(), `"status":"success"`)
			}

			// 验证模拟对象调用
			mockService.AssertExpectations(t)
			mockLogger.AssertExpectations(t)
		})
	}
}

func TestPaymentHandler_HealthCheck(t *testing.T) {
	helper := testutil.NewHTTPTestHelper(t)
	helper.SetupGinTestMode()

	// 创建模拟对象（健康检查不需要依赖）
	mockService := &testutil.MockPaymentService{}
	mockLogger := &testutil.MockLogger{}

	// 创建处理器
	handler := NewPaymentHandler(mockService, mockLogger)

	// 设置路由
	router := gin.New()
	router.GET("/health", handler.HealthCheck)

	// 创建请求
	req := helper.CreateJSONRequest("GET", "/health", nil)

	// 执行请求
	w := helper.PerformRequest(router, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json; charset=utf-8", w.Header().Get("Content-Type"))

	// 验证响应内容
	expectedResponse := map[string]string{
		"status":  "healthy",
		"service": "payment-backend",
	}
	helper.AssertJSONResponse(w, http.StatusOK, expectedResponse)
}
