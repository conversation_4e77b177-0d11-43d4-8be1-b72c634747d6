package handler

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"payment-backend/internal/config"
	"payment-backend/internal/domain"
	"payment-backend/internal/handler/testutil"
	"payment-backend/internal/logger"
	"payment-backend/internal/repository/memory"
	"payment-backend/internal/service"
)

// IntegrationTestSuite 集成测试套件
type IntegrationTestSuite struct {
	suite.Suite
	router         *gin.Engine
	paymentHandler *PaymentHandler
	helper         *testutil.HTTPTestHelper
	testData       *testutil.TestDataBuilder
}

// SetupSuite 设置测试套件
func (suite *IntegrationTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)

	// 创建测试依赖
	suite.helper = testutil.NewHTTPTestHelper(suite.T())
	suite.testData = testutil.NewTestDataBuilder()

	// 创建内存仓储
	paymentRepo := memory.NewPaymentRepository()
	sessionRepo := memory.NewCheckoutSessionRepository()

	// 创建模拟网关
	gateways := map[domain.PaymentProvider]domain.PaymentGateway{
		domain.PaymentProviderStripe: &MockGateway{},
		domain.PaymentProviderPayPal: &MockGateway{},
	}

	// 创建日志记录器
	logger, _ := logger.NewLogger(&config.LogConfig{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	})

	// 创建服务
	paymentService := service.NewPaymentService(paymentRepo, sessionRepo, gateways, logger)

	// 创建处理器
	suite.paymentHandler = NewPaymentHandler(paymentService, logger)

	// 设置路由
	suite.router = gin.New()
	suite.setupRoutes()
}

// setupRoutes 设置路由
func (suite *IntegrationTestSuite) setupRoutes() {
	// 健康检查
	suite.router.GET("/health", suite.paymentHandler.HealthCheck)

	// API v1 路由组
	v1 := suite.router.Group("/api/v1")
	{
		// 支付服务路由组
		payService := v1.Group("/pay-service")
		{
			// 结账会话
			payService.POST("/checkout/session", suite.paymentHandler.CreateCheckoutSession)

			// 支付信息
			payService.GET("/payments/:id", suite.paymentHandler.GetPayment)
			payService.GET("/sessions/:session_id/payment", suite.paymentHandler.GetPaymentBySessionID)

			// Webhook
			payService.POST("/webhooks/:provider", suite.paymentHandler.ProcessWebhook)
		}
	}
}

// TestCreateCheckoutSessionIntegration 测试创建结账会话集成
func (suite *IntegrationTestSuite) TestCreateCheckoutSessionIntegration() {
	// 创建有效请求
	req := suite.testData.NewCreateCheckoutSessionRequestBuilder().
		WithCustomerID(testutil.TestCustomerID).
		WithAmount(testutil.TestAmount).
		WithDescription(testutil.TestDescription).
		Build()

	// 发送请求
	httpReq := suite.helper.CreateJSONRequest("POST", "/api/v1/pay-service/checkout/session", req)
	w := suite.helper.PerformRequest(suite.router, httpReq)

	// 验证响应
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// 解析响应
	var response domain.CreateCheckoutSessionResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// 验证响应内容
	assert.NotEmpty(suite.T(), response.SessionID)
	assert.NotEqual(suite.T(), uuid.Nil, response.PaymentID)
	assert.NotEmpty(suite.T(), response.CheckoutURL)
	assert.False(suite.T(), response.ExpiresAt.IsZero())
}

// TestGetPaymentIntegration 测试获取支付信息集成
func (suite *IntegrationTestSuite) TestGetPaymentIntegration() {
	// 首先创建一个结账会话
	createReq := suite.testData.NewCreateCheckoutSessionRequestBuilder().
		WithCustomerID(testutil.TestCustomerID).
		Build()

	httpReq := suite.helper.CreateJSONRequest("POST", "/api/v1/pay-service/checkout/session", createReq)
	w := suite.helper.PerformRequest(suite.router, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var createResponse domain.CreateCheckoutSessionResponse
	err := json.Unmarshal(w.Body.Bytes(), &createResponse)
	assert.NoError(suite.T(), err)

	// 获取支付信息
	getURL := "/api/v1/pay-service/payments/" + createResponse.PaymentID.String()
	httpReq = suite.helper.CreateJSONRequest("GET", getURL, nil)
	w = suite.helper.PerformRequest(suite.router, httpReq)

	// 验证响应
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var payment domain.Payment
	err = json.Unmarshal(w.Body.Bytes(), &payment)
	assert.NoError(suite.T(), err)

	// 验证支付信息
	assert.Equal(suite.T(), createResponse.PaymentID, payment.ID)
	assert.Equal(suite.T(), createResponse.SessionID, payment.SessionID)
	assert.Equal(suite.T(), testutil.TestCustomerID, payment.CustomerID)
}

// TestGetPaymentBySessionIDIntegration 测试根据会话ID获取支付信息集成
func (suite *IntegrationTestSuite) TestGetPaymentBySessionIDIntegration() {
	// 首先创建一个结账会话
	createReq := suite.testData.NewCreateCheckoutSessionRequestBuilder().
		WithCustomerID(testutil.TestCustomerID).
		Build()

	httpReq := suite.helper.CreateJSONRequest("POST", "/api/v1/pay-service/checkout/session", createReq)
	w := suite.helper.PerformRequest(suite.router, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var createResponse domain.CreateCheckoutSessionResponse
	err := json.Unmarshal(w.Body.Bytes(), &createResponse)
	assert.NoError(suite.T(), err)

	// 根据会话ID获取支付信息
	getURL := "/api/v1/pay-service/sessions/" + createResponse.SessionID + "/payment"
	httpReq = suite.helper.CreateJSONRequest("GET", getURL, nil)
	w = suite.helper.PerformRequest(suite.router, httpReq)

	// 验证响应
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var payment domain.Payment
	err = json.Unmarshal(w.Body.Bytes(), &payment)
	assert.NoError(suite.T(), err)

	// 验证支付信息
	assert.Equal(suite.T(), createResponse.PaymentID, payment.ID)
	assert.Equal(suite.T(), createResponse.SessionID, payment.SessionID)
}

// TestHealthCheckIntegration 测试健康检查集成
func (suite *IntegrationTestSuite) TestHealthCheckIntegration() {
	httpReq := suite.helper.CreateJSONRequest("GET", "/health", nil)
	w := suite.helper.PerformRequest(suite.router, httpReq)

	// 验证响应
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]string
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.Equal(suite.T(), "healthy", response["status"])
	assert.Equal(suite.T(), "payment-backend", response["service"])
}

// TestInvalidRequestsIntegration 测试无效请求集成
func (suite *IntegrationTestSuite) TestInvalidRequestsIntegration() {
	// 测试无效的支付ID
	httpReq := suite.helper.CreateJSONRequest("GET", "/api/v1/pay-service/payments/invalid-uuid", nil)
	w := suite.helper.PerformRequest(suite.router, httpReq)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// 测试不存在的支付ID
	nonExistentID := uuid.New().String()
	httpReq = suite.helper.CreateJSONRequest("GET", "/api/v1/pay-service/payments/"+nonExistentID, nil)
	w = suite.helper.PerformRequest(suite.router, httpReq)
	assert.Equal(suite.T(), http.StatusNotFound, w.Code)

	// 测试空的会话ID
	httpReq = suite.helper.CreateJSONRequest("GET", "/api/v1/pay-service/sessions//payment", nil)
	w = suite.helper.PerformRequest(suite.router, httpReq)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

// MockGateway 模拟支付网关
type MockGateway struct{}

func (g *MockGateway) CreateCheckoutSession(session *domain.CheckoutSession) (string, error) {
	return "https://mock-checkout.example.com/" + session.ID, nil
}

func (g *MockGateway) GetPaymentStatus(providerPaymentID string) (domain.PaymentStatus, error) {
	return domain.PaymentStatusPending, nil
}

func (g *MockGateway) RefundPayment(providerPaymentID string, amount int64) error {
	return nil
}

func (g *MockGateway) VerifyWebhook(payload []byte, signature string) error {
	if signature == "" {
		return domain.ErrInvalidWebhookSignature
	}
	return nil
}

// TestIntegrationSuite 运行集成测试套件
func TestIntegrationSuite(t *testing.T) {
	suite.Run(t, new(IntegrationTestSuite))
}
